import React from 'react';
import { View, StyleSheet, Dimensions } from 'react-native';
import { Cell } from '@/types/game';
import { COLORS } from '@/utils/gameLogic';
import GameCell from './GameCell';

interface GameBoardProps {
  grid: Cell[][];
  onCellPress: (cell: Cell) => void;
  disabled?: boolean;
}

const { width: screenWidth } = Dimensions.get('window');
const BOARD_PADDING = 32;
const BOARD_SIZE = screenWidth - BOARD_PADDING;

export default function GameBoard({ grid, onCellPress, disabled = false }: GameBoardProps) {
  const gridSize = grid.length;
  const cellSize = (BOARD_SIZE - (gridSize + 1) * 2) / gridSize;

  return (
    <View style={[styles.container, { width: BOARD_SIZE, height: BOARD_SIZE }]}>
      {grid.map((row, rowIndex) =>
        row.map((cell, colIndex) => (
          <GameCell
            key={`${rowIndex}-${colIndex}`}
            cell={cell}
            size={cellSize}
            color={COLORS[cell.color]}
            onPress={() => !disabled && onCellPress(cell)}
            disabled={disabled}
            style={{
              position: 'absolute',
              left: colIndex * (cellSize + 2) + 2,
              top: rowIndex * (cellSize + 2) + 2,
            }}
          />
        ))
      )}
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#1f2937',
    borderRadius: 16,
    padding: 2,
    alignSelf: 'center',
  },
});
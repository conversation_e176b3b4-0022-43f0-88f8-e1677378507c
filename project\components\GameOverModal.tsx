import React from 'react';
import { 
  View, 
  Text, 
  Pressable, 
  StyleSheet, 
  Modal,
  Dimensions,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Trophy, RotateCcw, Chrome as Home } from 'lucide-react-native';
import { BlurView } from 'expo-blur';

interface GameOverModalProps {
  visible: boolean;
  isWon: boolean;
  score: number;
  movesUsed: number;
  timeUsed: number;
  onRestart: () => void;
  onHome: () => void;
}

const { width: screenWidth } = Dimensions.get('window');

export default function GameOverModal({
  visible,
  isWon,
  score,
  movesUsed,
  timeUsed,
  onRestart,
  onHome,
}: GameOverModalProps) {
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  return (
    <Modal
      visible={visible}
      transparent
      animationType="fade"
      statusBarTranslucent
    >
      <BlurView intensity={80} style={styles.overlay}>
        <View style={styles.modal}>
          <LinearGradient
            colors={isWon ? ['#10b981', '#059669'] : ['#ef4444', '#dc2626']}
            style={styles.header}
          >
            <Trophy size={48} color="#ffffff" />
            <Text style={styles.title}>
              {isWon ? 'Victory!' : 'Game Over'}
            </Text>
            <Text style={styles.subtitle}>
              {isWon 
                ? 'Congratulations! You flooded the board!' 
                : 'Better luck next time!'
              }
            </Text>
          </LinearGradient>

          <View style={styles.stats}>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Final Score</Text>
              <Text style={styles.statValue}>{score.toLocaleString()}</Text>
            </View>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Moves Used</Text>
              <Text style={styles.statValue}>{movesUsed}</Text>
            </View>
            <View style={styles.statRow}>
              <Text style={styles.statLabel}>Time Taken</Text>
              <Text style={styles.statValue}>{formatTime(timeUsed)}</Text>
            </View>
          </View>

          <View style={styles.buttons}>
            <Pressable
              style={({ pressed }) => [
                styles.button,
                pressed && styles.buttonPressed,
              ]}
              onPress={onRestart}
            >
              <LinearGradient
                colors={['#3b82f6', '#1d4ed8']}
                style={styles.buttonGradient}
              >
                <RotateCcw size={20} color="#ffffff" />
                <Text style={styles.buttonText}>Play Again</Text>
              </LinearGradient>
            </Pressable>

            <Pressable
              style={({ pressed }) => [
                styles.button,
                pressed && styles.buttonPressed,
              ]}
              onPress={onHome}
            >
              <LinearGradient
                colors={['#6b7280', '#4b5563']}
                style={styles.buttonGradient}
              >
                <Home size={20} color="#ffffff" />
                <Text style={styles.buttonText}>Main Menu</Text>
              </LinearGradient>
            </Pressable>
          </View>
        </View>
      </BlurView>
    </Modal>
  );
}

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: 'rgba(0, 0, 0, 0.5)',
  },
  modal: {
    width: screenWidth - 64,
    backgroundColor: '#ffffff',
    borderRadius: 20,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 10,
    },
    shadowOpacity: 0.25,
    shadowRadius: 10,
    elevation: 10,
  },
  header: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 24,
  },
  title: {
    fontSize: 28,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 16,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.9)',
    marginTop: 8,
    textAlign: 'center',
  },
  stats: {
    padding: 24,
    gap: 16,
  },
  statRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#6b7280',
  },
  statValue: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#1f2937',
  },
  buttons: {
    flexDirection: 'row',
    padding: 24,
    paddingTop: 0,
    gap: 12,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonPressed: {
    opacity: 0.8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 16,
    gap: 8,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});
import React from 'react';
import { Pressable, StyleSheet, ViewStyle } from 'react-native';
import Animated, { 
  useSharedValue, 
  useAnimatedStyle, 
  withTiming,
  withSequence,
  withSpring,
} from 'react-native-reanimated';
import { Cell } from '@/types/game';

interface GameCellProps {
  cell: Cell;
  size: number;
  color: string;
  onPress: () => void;
  disabled?: boolean;
  style?: ViewStyle;
}

const AnimatedPressable = Animated.createAnimatedComponent(Pressable);

export default function GameCell({ 
  cell, 
  size, 
  color, 
  onPress, 
  disabled = false,
  style 
}: GameCellProps) {
  const scale = useSharedValue(1);
  const opacity = useSharedValue(cell.isHomo ? 0.8 : 1);

  const animatedStyle = useAnimatedStyle(() => ({
    transform: [{ scale: scale.value }],
    opacity: opacity.value,
  }));

  const handlePressIn = () => {
    scale.value = withSpring(0.95);
  };

  const handlePressOut = () => {
    scale.value = withSpring(1);
  };

  const handlePress = () => {
    scale.value = withSequence(
      withTiming(0.9, { duration: 100 }),
      withTiming(1, { duration: 100 })
    );
    onPress();
  };

  React.useEffect(() => {
    opacity.value = withTiming(cell.isHomo ? 0.8 : 1, { duration: 200 });
  }, [cell.isHomo]);

  return (
    <AnimatedPressable
      style={[
        styles.cell,
        {
          width: size,
          height: size,
          backgroundColor: color,
        },
        animatedStyle,
        style,
      ]}
      onPressIn={handlePressIn}
      onPressOut={handlePressOut}
      onPress={handlePress}
      disabled={disabled}
    >
      {cell.isHomo && (
        <Animated.View 
          style={[
            styles.homoIndicator,
            { 
              backgroundColor: 'rgba(255, 255, 255, 0.3)',
            }
          ]} 
        />
      )}
    </AnimatedPressable>
  );
}

const styles = StyleSheet.create({
  cell: {
    borderRadius: 8,
    justifyContent: 'center',
    alignItems: 'center',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.25,
    shadowRadius: 3.84,
    elevation: 5,
  },
  homoIndicator: {
    position: 'absolute',
    top: 4,
    right: 4,
    width: 8,
    height: 8,
    borderRadius: 4,
  },
});
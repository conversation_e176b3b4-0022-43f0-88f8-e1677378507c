import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView,
  ScrollView,
  Pressable,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Trophy, Medal, Award, Star } from 'lucide-react-native';

interface ScoreEntry {
  id: string;
  score: number;
  difficulty: 'easy' | 'medium' | 'hard';
  moves: number;
  time: string;
  date: string;
}

export default function LeaderboardScreen() {
  const [selectedDifficulty, setSelectedDifficulty] = useState<'easy' | 'medium' | 'hard'>('medium');

  // Mock data - in a real app, this would come from storage/server
  const mockScores: ScoreEntry[] = [
    {
      id: '1',
      score: 2850,
      difficulty: 'medium',
      moves: 15,
      time: '1:23',
      date: '2025-01-10',
    },
    {
      id: '2',
      score: 2340,
      difficulty: 'medium',
      moves: 18,
      time: '1:45',
      date: '2025-01-09',
    },
    {
      id: '3',
      score: 1980,
      difficulty: 'medium',
      moves: 20,
      time: '2:10',
      date: '2025-01-08',
    },
    {
      id: '4',
      score: 3240,
      difficulty: 'easy',
      moves: 20,
      time: '1:15',
      date: '2025-01-10',
    },
    {
      id: '5',
      score: 1650,
      difficulty: 'hard',
      moves: 19,
      time: '0:55',
      date: '2025-01-07',
    },
  ];

  const filteredScores = mockScores
    .filter(score => score.difficulty === selectedDifficulty)
    .sort((a, b) => b.score - a.score);

  const getRankIcon = (index: number) => {
    switch (index) {
      case 0:
        return <Trophy size={24} color="#f59e0b" />;
      case 1:
        return <Medal size={24} color="#9ca3af" />;
      case 2:
        return <Award size={24} color="#cd7c2f" />;
      default:
        return <Star size={24} color="#6b7280" />;
    }
  };

  const difficultyColors = {
    easy: ['#10b981', '#065f46'],
    medium: ['#f59e0b', '#92400e'],
    hard: ['#ef4444', '#991b1b'],
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0f172a', '#1e293b', '#334155']}
        style={styles.background}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Leaderboard</Text>
          <Text style={styles.subtitle}>Your best scores and achievements</Text>
        </View>

        <View style={styles.difficultyTabs}>
          {(['easy', 'medium', 'hard'] as const).map((difficulty) => (
            <Pressable
              key={difficulty}
              style={({ pressed }) => [
                styles.tab,
                selectedDifficulty === difficulty && styles.activeTab,
                pressed && styles.tabPressed,
              ]}
              onPress={() => setSelectedDifficulty(difficulty)}
            >
              <LinearGradient
                colors={
                  selectedDifficulty === difficulty
                    ? difficultyColors[difficulty]
                    : ['#374151', '#4b5563']
                }
                style={styles.tabGradient}
              >
                <Text style={[
                  styles.tabText,
                  selectedDifficulty === difficulty && styles.activeTabText,
                ]}>
                  {difficulty.charAt(0).toUpperCase() + difficulty.slice(1)}
                </Text>
              </LinearGradient>
            </Pressable>
          ))}
        </View>

        <ScrollView style={styles.scoresContainer} showsVerticalScrollIndicator={false}>
          {filteredScores.length > 0 ? (
            filteredScores.map((score, index) => (
              <View key={score.id} style={styles.scoreCard}>
                <LinearGradient
                  colors={index === 0 ? ['#1f2937', '#111827'] : ['#374151', '#4b5563']}
                  style={styles.scoreGradient}
                >
                  <View style={styles.scoreHeader}>
                    <View style={styles.rankContainer}>
                      {getRankIcon(index)}
                      <Text style={styles.rankText}>#{index + 1}</Text>
                    </View>
                    <Text style={styles.scoreValue}>
                      {score.score.toLocaleString()}
                    </Text>
                  </View>

                  <View style={styles.scoreDetails}>
                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Moves</Text>
                      <Text style={styles.detailValue}>{score.moves}</Text>
                    </View>
                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Time</Text>
                      <Text style={styles.detailValue}>{score.time}</Text>
                    </View>
                    <View style={styles.detailItem}>
                      <Text style={styles.detailLabel}>Date</Text>
                      <Text style={styles.detailValue}>
                        {new Date(score.date).toLocaleDateString()}
                      </Text>
                    </View>
                  </View>
                </LinearGradient>
              </View>
            ))
          ) : (
            <View style={styles.emptyState}>
              <Trophy size={64} color="#6b7280" />
              <Text style={styles.emptyTitle}>No scores yet</Text>
              <Text style={styles.emptyText}>
                Play some games in {selectedDifficulty} mode to see your scores here!
              </Text>
            </View>
          )}
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#94a3b8',
    textAlign: 'center',
  },
  difficultyTabs: {
    flexDirection: 'row',
    marginHorizontal: 16,
    marginBottom: 24,
    gap: 8,
  },
  tab: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  activeTab: {
    shadowColor: '#3b82f6',
    shadowOffset: {
      width: 0,
      height: 2,
    },
    shadowOpacity: 0.3,
    shadowRadius: 4,
    elevation: 4,
  },
  tabPressed: {
    opacity: 0.8,
  },
  tabGradient: {
    paddingVertical: 12,
    paddingHorizontal: 16,
    alignItems: 'center',
  },
  tabText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#9ca3af',
  },
  activeTabText: {
    color: '#ffffff',
  },
  scoresContainer: {
    flex: 1,
    paddingHorizontal: 16,
  },
  scoreCard: {
    marginBottom: 12,
    borderRadius: 16,
    overflow: 'hidden',
  },
  scoreGradient: {
    padding: 16,
  },
  scoreHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    marginBottom: 12,
  },
  rankContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 8,
  },
  rankText: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  scoreValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#10b981',
  },
  scoreDetails: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  detailItem: {
    alignItems: 'center',
  },
  detailLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#9ca3af',
    marginBottom: 4,
  },
  detailValue: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
  emptyState: {
    alignItems: 'center',
    paddingVertical: 64,
    paddingHorizontal: 32,
  },
  emptyTitle: {
    fontSize: 20,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 16,
    marginBottom: 8,
  },
  emptyText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 20,
  },
});
import React from 'react';
import { View, Text, Pressable, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Pause, Play, RotateCcw } from 'lucide-react-native';

interface GameControlsProps {
  isPaused: boolean;
  onPause: () => void;
  onResume: () => void;
  onRestart: () => void;
  disabled?: boolean;
}

export default function GameControls({
  isPaused,
  onPause,
  onResume,
  onRestart,
  disabled = false,
}: GameControlsProps) {
  return (
    <View style={styles.container}>
      <Pressable
        style={({ pressed }) => [
          styles.button,
          pressed && styles.buttonPressed,
        ]}
        onPress={isPaused ? onResume : onPause}
        disabled={disabled}
      >
        <LinearGradient
          colors={['#3b82f6', '#1d4ed8']}
          style={styles.buttonGradient}
        >
          {isPaused ? (
            <Play size={20} color="#ffffff" />
          ) : (
            <Pause size={20} color="#ffffff" />
          )}
          <Text style={styles.buttonText}>
            {isPaused ? 'Resume' : 'Pause'}
          </Text>
        </LinearGradient>
      </Pressable>

      <Pressable
        style={({ pressed }) => [
          styles.button,
          pressed && styles.buttonPressed,
        ]}
        onPress={onRestart}
      >
        <LinearGradient
          colors={['#6b7280', '#4b5563']}
          style={styles.buttonGradient}
        >
          <RotateCcw size={20} color="#ffffff" />
          <Text style={styles.buttonText}>Restart</Text>
        </LinearGradient>
      </Pressable>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    flexDirection: 'row',
    justifyContent: 'center',
    gap: 16,
    paddingHorizontal: 16,
    marginVertical: 16,
  },
  button: {
    flex: 1,
    borderRadius: 12,
    overflow: 'hidden',
  },
  buttonPressed: {
    opacity: 0.8,
  },
  buttonGradient: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 20,
    gap: 8,
  },
  buttonText: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
  },
});
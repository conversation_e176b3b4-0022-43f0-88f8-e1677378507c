import React, { useState, useEffect, useRef } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView,
  Alert,
  Platform,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import * as Haptics from 'expo-haptics';
import { ColorFloodGame } from '@/utils/gameLogic';
import { Difficulty } from '@/types/game';
import GameBoard from '@/components/GameBoard';
import GameHeader from '@/components/GameHeader';
import GameControls from '@/components/GameControls';
import GameOverModal from '@/components/GameOverModal';
import DifficultySelector from '@/components/DifficultySelector';

export default function GameScreen() {
  const gameRef = useRef<ColorFloodGame>();
  const [gameState, setGameState] = useState(() => {
    gameRef.current = new ColorFloodGame('medium');
    return gameRef.current.getState();
  });
  const [gameConfig] = useState(() => gameRef.current!.getConfig());
  const [showDifficultySelector, setShowDifficultySelector] = useState(true);
  const [currentDifficulty, setCurrentDifficulty] = useState<Difficulty>('medium');

  const triggerHapticFeedback = () => {
    if (Platform.OS !== 'web') {
      Haptics.impactAsync(Haptics.ImpactFeedbackStyle.Light);
    }
  };

  const updateGameState = () => {
    setGameState(gameRef.current!.getState());
  };

  const startGame = (difficulty: Difficulty) => {
    gameRef.current = new ColorFloodGame(difficulty);
    setCurrentDifficulty(difficulty);
    setGameState(gameRef.current.getState());
    setShowDifficultySelector(false);
    
    gameRef.current.startTimer((timeRemaining) => {
      setGameState(prev => ({ ...prev, timeRemaining }));
    });
  };

  const handleCellPress = (cell: any) => {
    if (gameRef.current?.handlePlayerTap(cell)) {
      triggerHapticFeedback();
      updateGameState();
    }
  };

  const handlePause = () => {
    gameRef.current?.pauseGame();
    updateGameState();
  };

  const handleResume = () => {
    gameRef.current?.resumeGame();
    updateGameState();
  };

  const handleRestart = () => {
    gameRef.current?.resetGame();
    updateGameState();
    
    gameRef.current?.startTimer((timeRemaining) => {
      setGameState(prev => ({ ...prev, timeRemaining }));
    });
  };

  const handleBackToMenu = () => {
    gameRef.current?.stopTimer();
    setShowDifficultySelector(true);
  };

  const getProgress = () => {
    return gameRef.current?.getProgress() || 0;
  };

  useEffect(() => {
    return () => {
      gameRef.current?.stopTimer();
    };
  }, []);

  if (showDifficultySelector) {
    return (
      <DifficultySelector
        onDifficultySelect={startGame}
        currentDifficulty={currentDifficulty}
      />
    );
  }

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0f172a', '#1e293b', '#334155']}
        style={styles.background}
      >
        <GameHeader
          movesUsed={gameState.moveCount}
          maxMoves={gameConfig.maxMoves}
          timeRemaining={gameState.timeRemaining}
          score={gameState.score}
          progress={getProgress()}
        />

        <View style={styles.gameContainer}>
          <GameBoard
            grid={gameState.grid}
            onCellPress={handleCellPress}
            disabled={gameState.isPaused || gameState.isGameOver || gameState.isWon}
          />
        </View>

        <GameControls
          isPaused={gameState.isPaused}
          onPause={handlePause}
          onResume={handleResume}
          onRestart={handleRestart}
          disabled={gameState.isGameOver || gameState.isWon}
        />

        <GameOverModal
          visible={gameState.isWon || gameState.isGameOver}
          isWon={gameState.isWon}
          score={gameState.score}
          movesUsed={gameState.moveCount}
          timeUsed={gameConfig.timeLimit - gameState.timeRemaining}
          onRestart={handleRestart}
          onHome={handleBackToMenu}
        />
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  gameContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 16,
  },
});
import React from 'react';
import { View, Text, StyleSheet } from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Clock, Target, Trophy } from 'lucide-react-native';

interface GameHeaderProps {
  movesUsed: number;
  maxMoves: number;
  timeRemaining: number;
  score: number;
  progress: number;
}

export default function GameHeader({
  movesUsed,
  maxMoves,
  timeRemaining,
  score,
  progress,
}: GameHeaderProps) {
  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return `${mins}:${secs.toString().padStart(2, '0')}`;
  };

  const movesRemaining = maxMoves - movesUsed;
  const isLowMoves = movesRemaining <= 5;
  const isLowTime = timeRemaining <= 15;

  return (
    <View style={styles.container}>
      <LinearGradient
        colors={['#1f2937', '#111827']}
        style={styles.gradient}
      >
        <View style={styles.statsRow}>
          <View style={styles.statItem}>
            <Target 
              size={20} 
              color={isLowMoves ? '#ef4444' : '#10b981'} 
            />
            <Text style={[
              styles.statValue,
              isLowMoves && styles.warningText
            ]}>
              {movesRemaining}
            </Text>
            <Text style={styles.statLabel}>moves left</Text>
          </View>

          <View style={styles.statItem}>
            <Clock 
              size={20} 
              color={isLowTime ? '#ef4444' : '#3b82f6'} 
            />
            <Text style={[
              styles.statValue,
              isLowTime && styles.warningText
            ]}>
              {formatTime(timeRemaining)}
            </Text>
            <Text style={styles.statLabel}>time left</Text>
          </View>

          <View style={styles.statItem}>
            <Trophy size={20} color="#f59e0b" />
            <Text style={styles.statValue}>{score}</Text>
            <Text style={styles.statLabel}>score</Text>
          </View>
        </View>

        <View style={styles.progressContainer}>
          <Text style={styles.progressLabel}>Progress</Text>
          <View style={styles.progressBar}>
            <View 
              style={[
                styles.progressFill, 
                { width: `${Math.round(progress * 100)}%` }
              ]} 
            />
          </View>
          <Text style={styles.progressText}>
            {Math.round(progress * 100)}%
          </Text>
        </View>
      </LinearGradient>
    </View>
  );
}

const styles = StyleSheet.create({
  container: {
    marginHorizontal: 16,
    marginVertical: 8,
    borderRadius: 16,
    overflow: 'hidden',
  },
  gradient: {
    paddingHorizontal: 20,
    paddingVertical: 16,
  },
  statsRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginBottom: 16,
  },
  statItem: {
    alignItems: 'center',
    flex: 1,
  },
  statValue: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginTop: 4,
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: '#9ca3af',
    marginTop: 2,
  },
  warningText: {
    color: '#ef4444',
  },
  progressContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    gap: 12,
  },
  progressLabel: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: '#e5e7eb',
  },
  progressBar: {
    flex: 1,
    height: 8,
    backgroundColor: '#374151',
    borderRadius: 4,
    overflow: 'hidden',
  },
  progressFill: {
    height: '100%',
    backgroundColor: '#10b981',
    borderRadius: 4,
  },
  progressText: {
    fontSize: 14,
    fontFamily: 'Inter-SemiBold',
    color: '#10b981',
    minWidth: 40,
    textAlign: 'right',
  },
});
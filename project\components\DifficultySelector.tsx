import React from 'react';
import { 
  View, 
  Text, 
  Pressable, 
  StyleSheet, 
  SafeAreaView,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Zap, Target, Clock } from 'lucide-react-native';
import { Difficulty } from '@/types/game';
import { DIFFICULTY_CONFIGS } from '@/utils/gameLogic';

interface DifficultySelectorProps {
  onDifficultySelect: (difficulty: Difficulty) => void;
  currentDifficulty: Difficulty;
}

export default function DifficultySelector({
  onDifficultySelect,
  currentDifficulty,
}: DifficultySelectorProps) {
  const difficultyInfo = {
    easy: {
      title: 'Easy',
      description: 'Perfect for beginners',
      icon: Target,
      colors: ['#10b981', '#065f46'],
    },
    medium: {
      title: 'Medium',
      description: 'Balanced challenge',
      icon: Zap,
      colors: ['#f59e0b', '#92400e'],
    },
    hard: {
      title: 'Hard',
      description: 'For color flood masters',
      icon: Clock,
      colors: ['#ef4444', '#991b1b'],
    },
  };

  const formatTime = (seconds: number): string => {
    const mins = Math.floor(seconds / 60);
    const secs = seconds % 60;
    return mins > 0 ? `${mins}:${secs.toString().padStart(2, '0')}` : `${secs}s`;
  };

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0f172a', '#1e293b', '#334155']}
        style={styles.background}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Color Flood</Text>
          <Text style={styles.subtitle}>
            Choose your difficulty level and flood the board with a single color!
          </Text>
        </View>

        <View style={styles.difficultyContainer}>
          {(Object.keys(difficultyInfo) as Difficulty[]).map((difficulty) => {
            const info = difficultyInfo[difficulty];
            const config = DIFFICULTY_CONFIGS[difficulty];
            const IconComponent = info.icon;
            const isSelected = difficulty === currentDifficulty;

            return (
              <Pressable
                key={difficulty}
                style={({ pressed }) => [
                  styles.difficultyCard,
                  pressed && styles.cardPressed,
                  isSelected && styles.selectedCard,
                ]}
                onPress={() => onDifficultySelect(difficulty)}
              >
                <LinearGradient
                  colors={info.colors}
                  style={styles.cardGradient}
                >
                  <View style={styles.cardHeader}>
                    <IconComponent size={32} color="#ffffff" />
                    <Text style={styles.difficultyTitle}>{info.title}</Text>
                  </View>
                  
                  <Text style={styles.difficultyDescription}>
                    {info.description}
                  </Text>

                  <View style={styles.statsContainer}>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Grid Size</Text>
                      <Text style={styles.statValue}>{config.size}×{config.size}</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Max Moves</Text>
                      <Text style={styles.statValue}>{config.maxMoves}</Text>
                    </View>
                    <View style={styles.statItem}>
                      <Text style={styles.statLabel}>Time Limit</Text>
                      <Text style={styles.statValue}>{formatTime(config.timeLimit)}</Text>
                    </View>
                  </View>
                </LinearGradient>
              </Pressable>
            );
          })}
        </View>

        <View style={styles.instructions}>
          <Text style={styles.instructionsTitle}>How to Play</Text>
          <Text style={styles.instructionsText}>
            • Tap any colored cell to flood the board with that color{'\n'}
            • Start from the top-left corner and expand your flooded region{'\n'}
            • Complete the board within the move and time limits{'\n'}
            • Higher scores for fewer moves and faster completion
          </Text>
        </View>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
    paddingHorizontal: 16,
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
  },
  title: {
    fontSize: 36,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    textAlign: 'center',
    marginBottom: 12,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#94a3b8',
    textAlign: 'center',
    lineHeight: 24,
  },
  difficultyContainer: {
    gap: 16,
    marginBottom: 32,
  },
  difficultyCard: {
    borderRadius: 16,
    overflow: 'hidden',
    shadowColor: '#000',
    shadowOffset: {
      width: 0,
      height: 4,
    },
    shadowOpacity: 0.3,
    shadowRadius: 8,
    elevation: 8,
  },
  selectedCard: {
    shadowColor: '#3b82f6',
    shadowOpacity: 0.5,
  },
  cardPressed: {
    opacity: 0.9,
  },
  cardGradient: {
    padding: 20,
  },
  cardHeader: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 8,
    gap: 12,
  },
  difficultyTitle: {
    fontSize: 24,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  difficultyDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.8)',
    marginBottom: 16,
  },
  statsContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
  },
  statItem: {
    alignItems: 'center',
  },
  statLabel: {
    fontSize: 12,
    fontFamily: 'Inter-Medium',
    color: 'rgba(255, 255, 255, 0.7)',
    marginBottom: 4,
  },
  statValue: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
  },
  instructions: {
    backgroundColor: 'rgba(255, 255, 255, 0.1)',
    borderRadius: 16,
    padding: 20,
    marginBottom: 32,
  },
  instructionsTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 12,
  },
  instructionsText: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#e2e8f0',
    lineHeight: 20,
  },
});
import React, { useState } from 'react';
import { 
  View, 
  Text, 
  StyleSheet, 
  SafeAreaView,
  ScrollView,
  Pressable,
  Switch,
} from 'react-native';
import { LinearGradient } from 'expo-linear-gradient';
import { Volume2, VolumeX, Vibrate, Info, CircleHelp as HelpCircle, Star, Share, RotateCcw } from 'lucide-react-native';

export default function SettingsScreen() {
  const [soundEnabled, setSoundEnabled] = useState(true);
  const [hapticEnabled, setHapticEnabled] = useState(true);
  const [showTutorial, setShowTutorial] = useState(false);

  const SettingItem = ({ 
    icon: IconComponent, 
    title, 
    description, 
    rightElement,
    onPress,
  }: {
    icon: any;
    title: string;
    description?: string;
    rightElement?: React.ReactNode;
    onPress?: () => void;
  }) => (
    <Pressable
      style={({ pressed }) => [
        styles.settingItem,
        pressed && styles.settingPressed,
      ]}
      onPress={onPress}
    >
      <View style={styles.settingLeft}>
        <View style={styles.iconContainer}>
          <IconComponent size={20} color="#3b82f6" />
        </View>
        <View style={styles.settingText}>
          <Text style={styles.settingTitle}>{title}</Text>
          {description && (
            <Text style={styles.settingDescription}>{description}</Text>
          )}
        </View>
      </View>
      {rightElement}
    </Pressable>
  );

  return (
    <SafeAreaView style={styles.container}>
      <LinearGradient
        colors={['#0f172a', '#1e293b', '#334155']}
        style={styles.background}
      >
        <View style={styles.header}>
          <Text style={styles.title}>Settings</Text>
          <Text style={styles.subtitle}>Customize your gaming experience</Text>
        </View>

        <ScrollView style={styles.content} showsVerticalScrollIndicator={false}>
          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Audio & Haptics</Text>
            <View style={styles.sectionContent}>
              <SettingItem
                icon={soundEnabled ? Volume2 : VolumeX}
                title="Sound Effects"
                description="Play sounds during gameplay"
                rightElement={
                  <Switch
                    value={soundEnabled}
                    onValueChange={setSoundEnabled}
                    trackColor={{ false: '#374151', true: '#3b82f6' }}
                    thumbColor={soundEnabled ? '#ffffff' : '#9ca3af'}
                  />
                }
              />
              <SettingItem
                icon={Vibrate}
                title="Haptic Feedback"
                description="Vibrate on cell taps and interactions"
                rightElement={
                  <Switch
                    value={hapticEnabled}
                    onValueChange={setHapticEnabled}
                    trackColor={{ false: '#374151', true: '#3b82f6' }}
                    thumbColor={hapticEnabled ? '#ffffff' : '#9ca3af'}
                  />
                }
              />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Game</Text>
            <View style={styles.sectionContent}>
              <SettingItem
                icon={RotateCcw}
                title="Reset Statistics"
                description="Clear all scores and progress"
                onPress={() => {
                  // TODO: Implement reset functionality
                }}
              />
              <SettingItem
                icon={HelpCircle}
                title="How to Play"
                description="Learn the rules and strategies"
                onPress={() => setShowTutorial(true)}
              />
            </View>
          </View>

          <View style={styles.section}>
            <Text style={styles.sectionTitle}>Support</Text>
            <View style={styles.sectionContent}>
              <SettingItem
                icon={Star}
                title="Rate the Game"
                description="Help us improve by leaving a review"
                onPress={() => {
                  // TODO: Open app store rating
                }}
              />
              <SettingItem
                icon={Share}
                title="Share with Friends"
                description="Invite others to play Color Flood"
                onPress={() => {
                  // TODO: Implement sharing
                }}
              />
              <SettingItem
                icon={Info}
                title="About"
                description="Version 1.0.0 • Made with ❤️"
              />
            </View>
          </View>

          <View style={styles.footer}>
            <Text style={styles.footerText}>
              Color Flood • A challenging puzzle game
            </Text>
            <Text style={styles.footerSubtext}>
              Flood the board with a single color within limited moves and time!
            </Text>
          </View>
        </ScrollView>
      </LinearGradient>
    </SafeAreaView>
  );
}

const styles = StyleSheet.create({
  container: {
    flex: 1,
  },
  background: {
    flex: 1,
  },
  header: {
    alignItems: 'center',
    paddingTop: 40,
    paddingBottom: 32,
    paddingHorizontal: 16,
  },
  title: {
    fontSize: 32,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  subtitle: {
    fontSize: 16,
    fontFamily: 'Inter-Medium',
    color: '#94a3b8',
    textAlign: 'center',
  },
  content: {
    flex: 1,
    paddingHorizontal: 16,
  },
  section: {
    marginBottom: 32,
  },
  sectionTitle: {
    fontSize: 18,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 16,
    paddingHorizontal: 4,
  },
  sectionContent: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
    borderRadius: 16,
    overflow: 'hidden',
  },
  settingItem: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'space-between',
    paddingHorizontal: 16,
    paddingVertical: 16,
    borderBottomWidth: 1,
    borderBottomColor: 'rgba(255, 255, 255, 0.1)',
  },
  settingPressed: {
    backgroundColor: 'rgba(255, 255, 255, 0.05)',
  },
  settingLeft: {
    flexDirection: 'row',
    alignItems: 'center',
    flex: 1,
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 12,
    backgroundColor: 'rgba(59, 130, 246, 0.1)',
    alignItems: 'center',
    justifyContent: 'center',
    marginRight: 12,
  },
  settingText: {
    flex: 1,
  },
  settingTitle: {
    fontSize: 16,
    fontFamily: 'Inter-SemiBold',
    color: '#ffffff',
    marginBottom: 2,
  },
  settingDescription: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
    lineHeight: 18,
  },
  footer: {
    alignItems: 'center',
    paddingVertical: 32,
    paddingHorizontal: 16,
  },
  footerText: {
    fontSize: 16,
    fontFamily: 'Inter-Bold',
    color: '#ffffff',
    marginBottom: 8,
  },
  footerSubtext: {
    fontSize: 14,
    fontFamily: 'Inter-Regular',
    color: '#9ca3af',
    textAlign: 'center',
    lineHeight: 20,
  },
});
import { Cell, GameConfig, GameState, Difficulty } from '@/types/game';

export const DIFFICULTY_CONFIGS: Record<Difficulty, GameConfig> = {
  easy: {
    size: 8,
    numColors: 4,
    maxMoves: 25,
    timeLimit: 120, // 2 minutes
  },
  medium: {
    size: 10,
    numColors: 5,
    maxMoves: 22,
    timeLimit: 90, // 1.5 minutes
  },
  hard: {
    size: 12,
    numColors: 6,
    maxMoves: 20,
    timeLimit: 60, // 1 minute
  },
};

export const COLORS = [
  '#ef4444', // red
  '#3b82f6', // blue
  '#10b981', // green
  '#f59e0b', // yellow
  '#8b5cf6', // purple
  '#ec4899', // pink
];

export class ColorFloodGame {
  private config: GameConfig;
  private state: GameState;
  private timerInterval?: NodeJS.Timeout;

  constructor(difficulty: Difficulty = 'medium') {
    this.config = DIFFICULTY_CONFIGS[difficulty];
    this.state = this.createInitialState();
  }

  private createInitialState(): GameState {
    const grid = this.initializeBoard();
    return {
      grid,
      moveCount: 0,
      isWon: false,
      isGameOver: false,
      timeRemaining: this.config.timeLimit,
      score: 0,
      isPaused: false,
    };
  }

  public initializeBoard(): Cell[][] {
    const { size, numColors } = this.config;
    const grid: Cell[][] = [];

    // Create the grid with random colors
    for (let row = 0; row < size; row++) {
      grid[row] = [];
      for (let col = 0; col < size; col++) {
        grid[row][col] = {
          row,
          col,
          color: Math.floor(Math.random() * numColors),
          isHomo: false,
        };
      }
    }

    // Set anchor cell
    grid[0][0].isHomo = true;

    // Initial flood propagation
    this.propagateFloodOnGrid(grid);

    return grid;
  }

  private propagateFloodOnGrid(grid: Cell[][]): void {
    const { size } = this.config;
    const targetColor = grid[0][0].color;
    const queue: Cell[] = [];
    const visited = new Set<string>();

    // Add all current homo cells to queue
    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        if (grid[row][col].isHomo) {
          queue.push(grid[row][col]);
          visited.add(`${row}-${col}`);
        }
      }
    }

    // BFS flood fill
    while (queue.length > 0) {
      const currentCell = queue.shift()!;
      const neighbors = this.getNeighbors(currentCell, grid);

      for (const neighbor of neighbors) {
        const key = `${neighbor.row}-${neighbor.col}`;
        if (!visited.has(key) && 
            !neighbor.isHomo && 
            neighbor.color === targetColor) {
          neighbor.isHomo = true;
          queue.push(neighbor);
          visited.add(key);
        }
      }
    }
  }

  private getNeighbors(cell: Cell, grid: Cell[][]): Cell[] {
    const { size } = this.config;
    const neighbors: Cell[] = [];
    const directions = [
      [-1, 0], [1, 0], [0, -1], [0, 1] // up, down, left, right
    ];

    for (const [dRow, dCol] of directions) {
      const newRow = cell.row + dRow;
      const newCol = cell.col + dCol;

      if (newRow >= 0 && newRow < size && newCol >= 0 && newCol < size) {
        neighbors.push(grid[newRow][newCol]);
      }
    }

    return neighbors;
  }

  public handlePlayerTap(tappedCell: Cell): boolean {
    if (this.state.isWon || this.state.isGameOver || this.state.isPaused) {
      return false;
    }

    const newColor = tappedCell.color;
    const oldColor = this.state.grid[0][0].color;

    if (newColor === oldColor) {
      return false; // No change needed
    }

    // Increment move count
    this.state.moveCount++;

    // Recolor homo region
    const { size } = this.config;
    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        if (this.state.grid[row][col].isHomo) {
          this.state.grid[row][col].color = newColor;
        }
      }
    }

    // Propagate flood
    this.propagateFloodOnGrid(this.state.grid);

    // Check win condition
    this.checkWinCondition();

    // Check game over condition
    if (this.state.moveCount >= this.config.maxMoves && !this.state.isWon) {
      this.state.isGameOver = true;
      this.stopTimer();
    }

    return true;
  }

  private checkWinCondition(): void {
    const { size } = this.config;
    let homoCount = 0;

    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        if (this.state.grid[row][col].isHomo) {
          homoCount++;
        }
      }
    }

    if (homoCount === size * size) {
      this.state.isWon = true;
      this.calculateScore();
      this.stopTimer();
    }
  }

  private calculateScore(): void {
    const movesBonus = Math.max(0, this.config.maxMoves - this.state.moveCount) * 100;
    const timeBonus = this.state.timeRemaining * 10;
    const difficultyMultiplier = this.config.size / 8; // Easy: 1x, Medium: 1.25x, Hard: 1.5x
    
    this.state.score = Math.round((movesBonus + timeBonus) * difficultyMultiplier);
  }

  public startTimer(onTick?: (timeRemaining: number) => void): void {
    this.stopTimer();
    this.timerInterval = setInterval(() => {
      if (!this.state.isPaused && this.state.timeRemaining > 0) {
        this.state.timeRemaining--;
        onTick?.(this.state.timeRemaining);

        if (this.state.timeRemaining === 0 && !this.state.isWon) {
          this.state.isGameOver = true;
          this.stopTimer();
        }
      }
    }, 1000);
  }

  public stopTimer(): void {
    if (this.timerInterval) {
      clearInterval(this.timerInterval);
      this.timerInterval = undefined;
    }
  }

  public pauseGame(): void {
    this.state.isPaused = true;
  }

  public resumeGame(): void {
    this.state.isPaused = false;
  }

  public resetGame(difficulty?: Difficulty): void {
    this.stopTimer();
    if (difficulty) {
      this.config = DIFFICULTY_CONFIGS[difficulty];
    }
    this.state = this.createInitialState();
  }

  public getState(): GameState {
    return { ...this.state };
  }

  public getConfig(): GameConfig {
    return { ...this.config };
  }

  public getProgress(): number {
    const { size } = this.config;
    let homoCount = 0;

    for (let row = 0; row < size; row++) {
      for (let col = 0; col < size; col++) {
        if (this.state.grid[row][col].isHomo) {
          homoCount++;
        }
      }
    }

    return homoCount / (size * size);
  }
}
export interface Cell {
  row: number;
  col: number;
  color: number;
  isHomo: boolean;
}

export interface GameConfig {
  size: number;
  numColors: number;
  maxMoves: number;
  timeLimit: number; // in seconds
}

export type Difficulty = 'easy' | 'medium' | 'hard';

export interface GameStats {
  movesUsed: number;
  timeRemaining: number;
  score: number;
  difficulty: Difficulty;
}

export interface GameState {
  grid: Cell[][];
  moveCount: number;
  isWon: boolean;
  isGameOver: boolean;
  timeRemaining: number;
  score: number;
  isPaused: boolean;
}